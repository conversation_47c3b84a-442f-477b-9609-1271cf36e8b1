#!/usr/bin/env python3
"""
Implementação Prática da Estratégia de Trading
Demonstra como aplicar a estratégia vencedora (Média Móvel) na prática
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class TradingStrategyImplementation:
    def __init__(self):
        self.results_dir = Path('results')
        self.figures_dir = self.results_dir / 'figures' / 'trading_strategy'
        self.csv_dir = self.results_dir / 'csv' / 'trading_strategy'
        
        # Configurar estilo dos gráficos
        plt.style.use('default')
    
    def calculate_moving_averages(self, df):
        """
        Calcula médias móveis para os dados
        """
        # Calcular preço médio OHLC
        df['Preco_Media_OHLC'] = (df['Open'] + df['High'] + df['Low'] + df['Close']) / 4
        
        # Calcular médias móveis
        df['MM10'] = df['Preco_Media_OHLC'].rolling(window=10).mean()
        df['MM25'] = df['Preco_Media_OHLC'].rolling(window=25).mean()
        df['MM50'] = df['Preco_Media_OHLC'].rolling(window=50).mean()
        df['MM100'] = df['Preco_Media_OHLC'].rolling(window=100).mean()
        df['MM200'] = df['Preco_Media_OHLC'].rolling(window=200).mean()
        
        return df
    
    def detect_trading_signals(self, df):
        """
        Detecta sinais de compra/venda baseados na estratégia de média móvel
        """
        signals = df.copy()
        signals['Buy_Signal'] = 0
        signals['Sell_Signal'] = 0
        signals['Signal_Type'] = ''
        signals['Signal_Description'] = ''
        
        # Colunas de médias móveis para comparação
        ma_columns = ['MM25', 'MM50', 'MM100', 'MM200']
        
        for i in range(1, len(signals)):
            buy_count = 0
            sell_count = 0
            signal_details = []
            
            for ma_col in ma_columns:
                if pd.notna(signals[ma_col].iloc[i]) and pd.notna(signals[ma_col].iloc[i-1]):
                    # Cruzamento de baixo para cima (MM10 estava abaixo, agora está acima) = COMPRA (sinal de alta)
                    if (signals['MM10'].iloc[i-1] < signals[ma_col].iloc[i-1] and
                        signals['MM10'].iloc[i] > signals[ma_col].iloc[i]):
                        buy_count += 1
                        signal_details.append(f"MM10 cruzou {ma_col} para cima")

                    # Cruzamento de cima para baixo (MM10 estava acima, agora está abaixo) = VENDA (sinal de baixa)
                    elif (signals['MM10'].iloc[i-1] > signals[ma_col].iloc[i-1] and
                          signals['MM10'].iloc[i] < signals[ma_col].iloc[i]):
                        sell_count += 1
                        signal_details.append(f"MM10 cruzou {ma_col} para baixo")
            
            signals['Buy_Signal'].iloc[i] = buy_count
            signals['Sell_Signal'].iloc[i] = sell_count
            
            if buy_count > 0:
                signals['Signal_Type'].iloc[i] = 'COMPRA'
                signals['Signal_Description'].iloc[i] = '; '.join(signal_details)
            elif sell_count > 0:
                signals['Signal_Type'].iloc[i] = 'VENDA'
                signals['Signal_Description'].iloc[i] = '; '.join(signal_details)
        
        # Calcular posição acumulada
        signals['Net_Signal'] = signals['Buy_Signal'] - signals['Sell_Signal']
        signals['Position'] = signals['Net_Signal'].cumsum()
        
        return signals
    
    def simulate_trading(self, signals, initial_capital=10000):
        """
        Simula trading com capital inicial
        """
        capital = initial_capital
        shares = 0
        portfolio_value = []
        cash_flow = []
        
        for i, row in signals.iterrows():
            # Executar compras
            if row['Buy_Signal'] > 0:
                shares_to_buy = row['Buy_Signal']
                cost = shares_to_buy * row['Preco_Media_OHLC']
                if capital >= cost:
                    capital -= cost
                    shares += shares_to_buy
                    cash_flow.append(-cost)
                else:
                    # Comprar o máximo possível
                    max_shares = int(capital / row['Preco_Media_OHLC'])
                    if max_shares > 0:
                        cost = max_shares * row['Preco_Media_OHLC']
                        capital -= cost
                        shares += max_shares
                        cash_flow.append(-cost)
                    else:
                        cash_flow.append(0)
            
            # Executar vendas
            elif row['Sell_Signal'] > 0:
                shares_to_sell = min(row['Sell_Signal'], shares)
                if shares_to_sell > 0:
                    revenue = shares_to_sell * row['Preco_Media_OHLC']
                    capital += revenue
                    shares -= shares_to_sell
                    cash_flow.append(revenue)
                else:
                    cash_flow.append(0)
            else:
                cash_flow.append(0)
            
            # Calcular valor total do portfólio
            total_value = capital + (shares * row['Preco_Media_OHLC'])
            portfolio_value.append(total_value)
        
        signals['Portfolio_Value'] = portfolio_value
        signals['Cash_Flow'] = cash_flow
        signals['Shares_Held'] = shares  # Valor final
        signals['Final_Capital'] = capital  # Valor final
        
        return signals
    
    def plot_trading_strategy(self, signals, stock_symbol):
        """
        Plota a estratégia de trading implementada
        """
        fig, axes = plt.subplots(3, 1, figsize=(16, 14))
        fig.suptitle(f'Implementação da Estratégia de Trading - {stock_symbol}', 
                    fontsize=16, fontweight='bold')
        
        # Gráfico 1: Preços e Médias Móveis com Sinais
        ax1 = axes[0]
        ax1.plot(signals.index, signals['Preco_Media_OHLC'], label='Preço', linewidth=2, color='black')
        ax1.plot(signals.index, signals['MM10'], label='MM10', linewidth=1.5, alpha=0.8)
        ax1.plot(signals.index, signals['MM25'], label='MM25', linewidth=1, alpha=0.7)
        ax1.plot(signals.index, signals['MM50'], label='MM50', linewidth=1, alpha=0.7)
        ax1.plot(signals.index, signals['MM100'], label='MM100', linewidth=1, alpha=0.7)
        ax1.plot(signals.index, signals['MM200'], label='MM200', linewidth=1, alpha=0.7)
        
        # Marcar sinais de compra e venda
        buy_signals = signals[signals['Buy_Signal'] > 0]
        sell_signals = signals[signals['Sell_Signal'] > 0]
        
        ax1.scatter(buy_signals.index, buy_signals['Preco_Media_OHLC'], 
                   color='green', marker='^', s=100, label='Compra', zorder=5)
        ax1.scatter(sell_signals.index, sell_signals['Preco_Media_OHLC'], 
                   color='red', marker='v', s=100, label='Venda', zorder=5)
        
        ax1.set_ylabel('Preço (R$)')
        ax1.set_title('Preços e Sinais de Trading')
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # Gráfico 2: Posição em Ações
        ax2 = axes[1]
        ax2.plot(signals.index, signals['Position'], linewidth=2, color='blue')
        ax2.fill_between(signals.index, signals['Position'], alpha=0.3, color='blue')
        ax2.set_ylabel('Número de Ações')
        ax2.set_title('Posição em Ações ao Longo do Tempo')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        # Gráfico 3: Valor do Portfólio
        ax3 = axes[2]
        ax3.plot(signals.index, signals['Portfolio_Value'], linewidth=2, color='purple')
        ax3.fill_between(signals.index, signals['Portfolio_Value'], alpha=0.3, color='purple')
        ax3.set_ylabel('Valor do Portfólio (R$)')
        ax3.set_xlabel('Data')
        ax3.set_title('Evolução do Valor do Portfólio')
        ax3.grid(True, alpha=0.3)
        
        # Adicionar linha de capital inicial
        initial_value = signals['Portfolio_Value'].iloc[0] if len(signals) > 0 else 10000
        ax3.axhline(y=initial_value, color='red', linestyle='--', alpha=0.7, 
                   label=f'Capital Inicial: R$ {initial_value:,.2f}')
        ax3.legend()
        
        plt.tight_layout()
        
        # Salvar gráfico
        filename = self.figures_dir / f'trading_implementation_{stock_symbol}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Gráfico de implementação salvo: {filename}")
    
    def generate_trading_report(self, signals, stock_symbol, initial_capital=10000):
        """
        Gera relatório detalhado da implementação
        """
        final_value = signals['Portfolio_Value'].iloc[-1]
        total_return = final_value - initial_capital
        return_pct = (total_return / initial_capital) * 100
        
        total_buy_signals = signals['Buy_Signal'].sum()
        total_sell_signals = signals['Sell_Signal'].sum()
        total_trades = total_buy_signals + total_sell_signals
        
        # Salvar sinais detalhados
        trading_signals = signals[signals['Signal_Type'] != ''].copy()
        trading_signals = trading_signals[['Signal_Type', 'Signal_Description', 'Preco_Media_OHLC', 
                                         'Buy_Signal', 'Sell_Signal', 'Position']]
        
        signals_file = self.csv_dir / f'trading_signals_{stock_symbol}.csv'
        trading_signals.to_csv(signals_file)
        
        # Relatório em texto
        report_file = self.csv_dir / f'trading_implementation_report_{stock_symbol}.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"RELATÓRIO DE IMPLEMENTAÇÃO - {stock_symbol}\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("RESUMO DA ESTRATÉGIA:\n")
            f.write("- Comprar 1 ação quando MM10 cruza de cima para baixo qualquer outra MM\n")
            f.write("- Vender 1 ação quando MM10 cruza de baixo para cima qualquer outra MM\n\n")
            
            f.write("RESULTADOS:\n")
            f.write(f"Capital inicial: R$ {initial_capital:,.2f}\n")
            f.write(f"Valor final: R$ {final_value:,.2f}\n")
            f.write(f"Retorno total: R$ {total_return:,.2f}\n")
            f.write(f"Retorno percentual: {return_pct:.2f}%\n")
            f.write(f"Total de trades: {total_trades}\n")
            f.write(f"Sinais de compra: {total_buy_signals}\n")
            f.write(f"Sinais de venda: {total_sell_signals}\n")
            f.write(f"Posição final: {signals['Position'].iloc[-1]} ações\n\n")
            
            f.write("PRINCIPAIS SINAIS DE TRADING:\n")
            for _, row in trading_signals.head(10).iterrows():
                f.write(f"{row.name.strftime('%Y-%m-%d')}: {row['Signal_Type']} - ")
                f.write(f"{row['Signal_Description']} (Preço: R$ {row['Preco_Media_OHLC']:.2f})\n")
        
        print(f"Relatório de implementação salvo: {report_file}")
        print(f"Sinais detalhados salvos: {signals_file}")
        
        return {
            'initial_capital': initial_capital,
            'final_value': final_value,
            'total_return': total_return,
            'return_pct': return_pct,
            'total_trades': total_trades
        }
    
    def implement_strategy(self, stock_symbol='EMBR3.SA', period='1y', initial_capital=10000):
        """
        Implementa a estratégia para uma ação específica
        """
        print(f"Implementando estratégia para {stock_symbol}...")
        
        # Baixar dados
        ticker = yf.Ticker(stock_symbol)
        df = ticker.history(period=period)
        
        if df.empty:
            print(f"Erro: Não foi possível obter dados para {stock_symbol}")
            return None
        
        # Calcular médias móveis
        df = self.calculate_moving_averages(df)
        
        # Detectar sinais
        signals = self.detect_trading_signals(df)
        
        # Simular trading
        signals = self.simulate_trading(signals, initial_capital)
        
        # Gerar visualizações e relatórios
        stock_code = stock_symbol.replace('.SA', '')
        self.plot_trading_strategy(signals, stock_code)
        results = self.generate_trading_report(signals, stock_code, initial_capital)
        
        print(f"\nResultados para {stock_symbol}:")
        print(f"Retorno: {results['return_pct']:.2f}%")
        print(f"Total de trades: {results['total_trades']}")
        
        return signals, results

if __name__ == "__main__":
    # Implementar para EMBR3 (sua ação atual)
    implementation = TradingStrategyImplementation()
    signals, results = implementation.implement_strategy('EMBR3.SA', period='1y', initial_capital=10000)
    
    # Implementar também para algumas outras ações de exemplo
    example_stocks = ['VALE3.SA', 'PETR3.SA', 'BBAS3.SA']
    
    print("\n" + "="*60)
    print("IMPLEMENTAÇÃO PARA OUTRAS AÇÕES:")
    print("="*60)
    
    for stock in example_stocks:
        try:
            signals, results = implementation.implement_strategy(stock, period='1y', initial_capital=10000)
            print()
        except Exception as e:
            print(f"Erro ao processar {stock}: {e}")
            continue
