#!/usr/bin/env python3
"""
Análise de Estratégia de Trading - Portfólio Combinado
<PERSON> to<PERSON> as ações como um portfólio único para avaliar performance agregada
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class PortfolioTradingAnalysis:
    def __init__(self):
        self.results_dir = Path('results')
        self.figures_dir = self.results_dir / 'figures' / 'trading_strategy'
        self.csv_dir = self.results_dir / 'csv' / 'trading_strategy'
        
        # Configurar estilo dos gráficos
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Lista de ações
        self.stocks = ['ABEV3', 'AZUL4', 'BBAS3', 'BEEF3', 'BRFS3', 'CPFE3', 'EMBR3', 
                      'GGBR4', 'GOLL4', 'HYPE3', 'JBSS3', 'KLBN11', 'MRFG3', 'PCAR3', 
                      'PETR3', 'RADL3', 'SUZB3', 'TIMS3', 'VALE3', 'WEGE3']
    
    def load_all_stock_data(self, strategy_type='butterworth'):
        """
        Carrega dados de todas as ações para uma estratégia
        """
        all_data = {}
        
        for stock in self.stocks:
            if strategy_type == 'butterworth':
                file_path = f'results/csv/butterworth_analysis/individual_stocks/butterworth_series_{stock}.csv'
                signal_cols = ['Butterworth_10_dias', 'Butterworth_25_dias', 'Butterworth_50_dias', 
                              'Butterworth_100_dias', 'Butterworth_200_dias']
            else:  # moving_average
                file_path = f'results/csv/mm_analysis/individual_stocks/mm_series_{stock}.csv'
                signal_cols = ['MM10_dias', 'MM25_dias', 'MM50_dias', 'MM100_dias', 'MM200_dias']
            
            try:
                df = pd.read_csv(file_path)
                df['Data'] = pd.to_datetime(df['Data'])
                df = df.tail(252)  # Último ano
                df['Stock'] = stock
                all_data[stock] = {
                    'data': df,
                    'signal_10': signal_cols[0],
                    'other_signals': signal_cols[1:]
                }
            except FileNotFoundError:
                print(f"Arquivo não encontrado para {stock}")
                continue
        
        return all_data
    
    def detect_portfolio_signals(self, all_data):
        """
        Detecta sinais de trading para o portfólio combinado
        """
        portfolio_signals = []
        
        for stock, stock_data in all_data.items():
            df = stock_data['data'].copy()
            signal_10 = stock_data['signal_10']
            other_signals = stock_data['other_signals']
            
            
            # Detectar cruzamentos para esta ação
            for i in range(1, len(df)):
                buy_signals = 0
                sell_signals = 0
                signal_details = []
                
                for other_signal in other_signals:
                    if (pd.notna(df[signal_10].iloc[i]) and pd.notna(df[signal_10].iloc[i-1]) and
                        pd.notna(df[other_signal].iloc[i]) and pd.notna(df[other_signal].iloc[i-1])):
                        
                        # Cruzamento de baixo para cima = COMPRA (sinal de alta)
                        if (df[signal_10].iloc[i-1] < df[other_signal].iloc[i-1] and
                            df[signal_10].iloc[i] > df[other_signal].iloc[i]):
                            buy_signals += 1
                            signal_details.append(f"{signal_10} cruzou {other_signal} para cima")

                        # Cruzamento de cima para baixo = VENDA (sinal de baixa)
                        elif (df[signal_10].iloc[i-1] > df[other_signal].iloc[i-1] and
                              df[signal_10].iloc[i] < df[other_signal].iloc[i]):
                            sell_signals += 1
                            signal_details.append(f"{signal_10} cruzou {other_signal} para baixo")
                
                if buy_signals > 0 or sell_signals > 0:
                    portfolio_signals.append({
                        'Date': df['Data'].iloc[i],
                        'Stock': stock,
                        'Price': df['Preco_Media_OHLC'].iloc[i],
                        'Buy_Signal': buy_signals,
                        'Sell_Signal': sell_signals,
                        'Signal_Details': '; '.join(signal_details)
                    })
        
        return pd.DataFrame(portfolio_signals).sort_values('Date')

    def _check_initial_position(self, stock, current_date):
        """
        Verifica se devemos comprar ações inicialmente baseado na posição da linha de 10 dias
        """
        try:
            # Carregar dados da ação
            if hasattr(self, '_loaded_data'):
                stock_data = self._loaded_data.get(stock)
            else:
                # Carregar dados se não estiverem em cache
                butterworth_file = f'results/csv/butterworth_analysis/individual_stocks/butterworth_series_{stock}.csv'
                mm_file = f'results/csv/mm_analysis/individual_stocks/mm_series_{stock}.csv'

                # Tentar carregar dados (usar o primeiro disponível)
                try:
                    df = pd.read_csv(butterworth_file)
                    signal_10 = 'Butterworth_10_dias'
                    other_signals = ['Butterworth_25_dias', 'Butterworth_100_dias']
                except:
                    df = pd.read_csv(mm_file)
                    signal_10 = 'MM10_dias'
                    other_signals = ['MM25_dias', 'MM100_dias']

                df['Data'] = pd.to_datetime(df['Data'])
                stock_data = {'data': df, 'signal_10': signal_10, 'other_signals': other_signals}

            # Encontrar a primeira linha de dados válida
            df = stock_data['data']
            signal_10 = stock_data['signal_10']
            other_signals = stock_data['other_signals']

            # Pegar a primeira linha com dados válidos
            first_valid_row = None
            for _, row in df.iterrows():
                if (pd.notna(row[signal_10]) and
                    all(pd.notna(row[col]) for col in other_signals)):
                    first_valid_row = row
                    break

            if first_valid_row is None:
                return 0

            # Contar quantas linhas estão abaixo da linha de 10 dias
            buy_count = 0
            signal_10_value = first_valid_row[signal_10]

            for other_signal in other_signals:
                other_value = first_valid_row[other_signal]
                if signal_10_value > other_value:  # Linha de 10 dias está acima
                    buy_count += 1

            return buy_count

        except Exception as e:
            print(f"Erro ao verificar posição inicial para {stock}: {e}")
            return 0

    def simulate_portfolio_trading(self, signals_df, initial_capital=100000, equal_weight=True):
        """
        Simula trading do portfólio com capital inicial
        """
        capital = initial_capital
        positions = {stock: 0 for stock in self.stocks}  # Número de ações de cada stock
        current_prices = {stock: 0 for stock in self.stocks}  # Preços atuais de cada ação
        portfolio_history = []

        # Rastreamento de investimento real
        total_invested = 0  # Total gasto em compras
        total_recovered = 0  # Total recuperado em vendas
        cumulative_invested = 0  # Investimento líquido acumulado

        # Valor inicial por ação se peso igual
        if equal_weight:
            capital_per_stock = initial_capital / len(self.stocks)

        # Flag para rastrear se já fizemos a inicialização para cada ação
        initialized_stocks = set()

        for _, signal in signals_df.iterrows():
            stock = signal['Stock']
            price = signal['Price']
            date = signal['Date']

            # Atualizar preço atual desta ação
            current_prices[stock] = price

            # INICIALIZAÇÃO: Se é a primeira vez que vemos esta ação, verificar posições iniciais
            if stock not in initialized_stocks:
                initialized_stocks.add(stock)

                # Carregar dados desta ação para verificar posição inicial
                initial_buy_signals = self._check_initial_position(stock, date)

                if initial_buy_signals > 0:
                    shares_to_buy = 1  # Sempre comprar 1 ação quando há sinal
                    cost = shares_to_buy * price

                    if equal_weight:
                        available_capital = capital_per_stock if capital >= cost else capital
                    else:
                        available_capital = capital

                    if available_capital >= cost:
                        capital -= cost
                        positions[stock] += shares_to_buy
                        total_invested += cost
                        cumulative_invested += cost
                        if equal_weight:
                            capital_per_stock = available_capital - cost

                        # Registrar compra inicial
                        portfolio_history.append({
                            'Date': date,
                            'Stock': stock,
                            'Action': 'INITIAL_BUY',
                            'Shares': shares_to_buy,
                            'Price': price,
                            'Cash': capital,
                            'Portfolio_Value': capital + sum(positions[s] * current_prices.get(s, 0) for s in positions if positions[s] > 0),
                            'Total_Invested': total_invested,
                            'Total_Recovered': total_recovered,
                            'Net_Invested': cumulative_invested,
                            'Positions': positions.copy(),
                            'Current_Prices': current_prices.copy()
                        })

            # Executar compras
            if signal['Buy_Signal'] > 0:
                shares_to_buy = 1  # Sempre comprar 1 ação quando há sinal
                cost = shares_to_buy * price

                if equal_weight:
                    # Usar capital disponível para esta ação
                    available_capital = capital_per_stock if capital >= cost else capital
                else:
                    available_capital = capital

                if available_capital >= cost:
                    capital -= cost
                    positions[stock] += shares_to_buy
                    # Rastrear investimento real
                    total_invested += cost
                    total_recovered -= cost  # Subtrair compra do total recuperado
                    cumulative_invested += cost
                    if equal_weight:
                        capital_per_stock = available_capital - cost

            # Executar vendas
            elif signal['Sell_Signal'] > 0:
                shares_to_sell = min(1, positions[stock])  # Sempre vender 1 ação quando há sinal
                if shares_to_sell > 0:
                    revenue = shares_to_sell * price
                    capital += revenue
                    positions[stock] -= shares_to_sell
                    # Rastrear recuperação real
                    total_recovered += revenue
                    total_invested -= revenue  # Subtrair venda do total investido
                    cumulative_invested -= revenue
                    if equal_weight:
                        capital_per_stock += revenue / len(self.stocks)

            # Calcular valor total do portfólio usando preços corretos
            portfolio_value = capital
            for stock_name, shares in positions.items():
                if shares > 0 and current_prices[stock_name] > 0:
                    portfolio_value += shares * current_prices[stock_name]

            portfolio_history.append({
                'Date': date,
                'Stock': stock,
                'Action': 'BUY' if signal['Buy_Signal'] > 0 else 'SELL',
                'Shares': signal['Buy_Signal'] if signal['Buy_Signal'] > 0 else signal['Sell_Signal'],
                'Price': price,
                'Cash': capital,
                'Portfolio_Value': portfolio_value,
                'Total_Invested': total_invested,
                'Total_Recovered': total_recovered,
                'Net_Invested': cumulative_invested,
                'Positions': positions.copy(),
                'Current_Prices': current_prices.copy()
            })

        # VENDER TODAS AS AÇÕES NO FINAL
        if len(portfolio_history) > 0:
            last_date = portfolio_history[-1]['Date']

            # Vender todas as posições restantes
            for stock_name, shares in positions.items():
                if shares > 0 and current_prices[stock_name] > 0:
                    final_price = current_prices[stock_name]
                    revenue = shares * final_price
                    capital += revenue
                    total_recovered += revenue
                    total_invested -= revenue  # Subtrair venda final do total investido
                    cumulative_invested -= revenue

                    # Adicionar registro da venda final
                    portfolio_history.append({
                        'Date': last_date,
                        'Stock': stock_name,
                        'Action': 'FINAL_SELL',
                        'Shares': shares,
                        'Price': final_price,
                        'Cash': capital,
                        'Portfolio_Value': capital,  # Agora só temos cash
                        'Total_Invested': total_invested,
                        'Total_Recovered': total_recovered,
                        'Net_Invested': cumulative_invested,
                        'Positions': {s: 0 for s in self.stocks},  # Todas zeradas
                        'Current_Prices': current_prices.copy()
                    })

                    # Zerar posição
                    positions[stock_name] = 0

        return pd.DataFrame(portfolio_history), positions
    
    def calculate_portfolio_metrics(self, portfolio_history, initial_capital):
        """
        Calcula métricas de performance do portfólio
        """
        if portfolio_history.empty:
            return {}
        
        final_value = portfolio_history['Portfolio_Value'].iloc[-1]
        total_return = final_value - initial_capital
        return_pct = (total_return / initial_capital) * 100
        
        # Calcular volatilidade dos retornos diários
        portfolio_history['Daily_Return'] = portfolio_history['Portfolio_Value'].pct_change()
        volatility = portfolio_history['Daily_Return'].std() * np.sqrt(252) * 100  # Anualizada
        
        # Sharpe ratio (assumindo taxa livre de risco = 0)
        avg_daily_return = portfolio_history['Daily_Return'].mean()
        sharpe_ratio = (avg_daily_return * 252) / (portfolio_history['Daily_Return'].std() * np.sqrt(252)) if portfolio_history['Daily_Return'].std() > 0 else 0
        
        # Drawdown máximo
        portfolio_history['Cumulative_Max'] = portfolio_history['Portfolio_Value'].cummax()
        portfolio_history['Drawdown'] = (portfolio_history['Portfolio_Value'] - portfolio_history['Cumulative_Max']) / portfolio_history['Cumulative_Max']
        max_drawdown = portfolio_history['Drawdown'].min() * 100
        
        total_trades = len(portfolio_history)
        buy_trades = len(portfolio_history[portfolio_history['Action'] == 'BUY'])
        sell_trades = len(portfolio_history[portfolio_history['Action'] == 'SELL'])
        
        # Métricas de investimento real
        final_total_invested = portfolio_history['Total_Invested'].iloc[-1] if 'Total_Invested' in portfolio_history.columns else 0
        final_total_recovered = portfolio_history['Total_Recovered'].iloc[-1] if 'Total_Recovered' in portfolio_history.columns else 0
        final_net_invested = portfolio_history['Net_Invested'].iloc[-1] if 'Net_Invested' in portfolio_history.columns else 0

        # Retorno baseado no investimento real
        real_return = final_value - initial_capital  # Retorno absoluto
        invested_return = (final_total_recovered - final_total_invested) if final_total_invested > 0 else 0  # Ganho/perda nas operações
        invested_return_pct = (invested_return / final_total_invested * 100) if final_total_invested > 0 else 0

        return {
            'initial_capital': initial_capital,
            'final_value': final_value,
            'total_return': total_return,
            'return_pct': return_pct,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'buy_trades': buy_trades,
            'sell_trades': sell_trades,
            'total_invested': final_total_invested,
            'total_recovered': final_total_recovered,
            'net_invested': final_net_invested,
            'invested_return': invested_return,
            'invested_return_pct': invested_return_pct
        }
    
    def plot_portfolio_comparison(self, butter_history, mm_history, butter_metrics, mm_metrics):
        """
        Plota comparação das estratégias de portfólio
        """
        fig, axes = plt.subplots(2, 2, figsize=(18, 12))
        fig.suptitle('Análise de Portfólio Combinado - Butterworth vs Média Móvel', 
                    fontsize=16, fontweight='bold')
        
        # Gráfico 1: Evolução do valor do portfólio
        ax1 = axes[0, 0]
        if not butter_history.empty:
            ax1.plot(butter_history['Date'], butter_history['Portfolio_Value'], 
                    label='Butterworth', linewidth=2, alpha=0.8)
        if not mm_history.empty:
            ax1.plot(mm_history['Date'], mm_history['Portfolio_Value'], 
                    label='Média Móvel', linewidth=2, alpha=0.8)
        
        ax1.axhline(y=butter_metrics.get('initial_capital', 100000), color='red', 
                   linestyle='--', alpha=0.7, label='Capital Inicial')
        ax1.set_ylabel('Valor do Portfólio (R$)')
        ax1.set_title('Evolução do Valor do Portfólio')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Gráfico 2: Número de trades por mês
        ax2 = axes[0, 1]
        
        if not butter_history.empty:
            butter_monthly = butter_history.groupby(butter_history['Date'].dt.to_period('M')).size()
            ax2.plot(butter_monthly.index.astype(str), butter_monthly.values, 
                    'o-', label='Butterworth', alpha=0.8)
        
        if not mm_history.empty:
            mm_monthly = mm_history.groupby(mm_history['Date'].dt.to_period('M')).size()
            ax2.plot(mm_monthly.index.astype(str), mm_monthly.values, 
                    's-', label='Média Móvel', alpha=0.8)
        
        ax2.set_ylabel('Número de Trades')
        ax2.set_title('Trades por Mês')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # Gráfico 3: Distribuição de trades por ação
        ax3 = axes[1, 0]
        
        if not butter_history.empty:
            butter_by_stock = butter_history['Stock'].value_counts()
            ax3.bar([x + ' (B)' for x in butter_by_stock.index[:10]], butter_by_stock.values[:10], 
                   alpha=0.7, label='Butterworth')
        
        if not mm_history.empty:
            mm_by_stock = mm_history['Stock'].value_counts()
            ax3.bar([x + ' (MM)' for x in mm_by_stock.index[:10]], mm_by_stock.values[:10], 
                   alpha=0.7, label='Média Móvel')
        
        ax3.set_ylabel('Número de Trades')
        ax3.set_title('Top 10 Ações por Número de Trades')
        ax3.legend()
        ax3.tick_params(axis='x', rotation=45)
        
        # Gráfico 4: Métricas comparativas
        ax4 = axes[1, 1]
        
        metrics_names = ['Retorno (%)', 'Volatilidade (%)', 'Sharpe Ratio', 'Max Drawdown (%)']
        butter_values = [butter_metrics.get('return_pct', 0), 
                        butter_metrics.get('volatility', 0),
                        butter_metrics.get('sharpe_ratio', 0), 
                        abs(butter_metrics.get('max_drawdown', 0))]
        mm_values = [mm_metrics.get('return_pct', 0), 
                    mm_metrics.get('volatility', 0),
                    mm_metrics.get('sharpe_ratio', 0), 
                    abs(mm_metrics.get('max_drawdown', 0))]
        
        x = np.arange(len(metrics_names))
        width = 0.35
        
        ax4.bar(x - width/2, butter_values, width, label='Butterworth', alpha=0.8)
        ax4.bar(x + width/2, mm_values, width, label='Média Móvel', alpha=0.8)
        
        ax4.set_ylabel('Valor')
        ax4.set_title('Métricas de Performance')
        ax4.set_xticks(x)
        ax4.set_xticklabels(metrics_names, rotation=45)
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Salvar gráfico
        filename = self.figures_dir / 'portfolio_combined_analysis.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Gráfico de portfólio combinado salvo: {filename}")
    
    def run_portfolio_analysis(self, initial_capital=100000):
        """
        Executa análise completa do portfólio
        """
        print("Iniciando análise de portfólio combinado...")
        print("=" * 60)
        
        # Carregar dados para ambas as estratégias
        print("Carregando dados Butterworth...")
        butter_data = self.load_all_stock_data('butterworth')
        
        print("Carregando dados Média Móvel...")
        mm_data = self.load_all_stock_data('moving_average')
        
        # Detectar sinais
        print("Detectando sinais Butterworth...")
        butter_signals = self.detect_portfolio_signals(butter_data)
        
        print("Detectando sinais Média Móvel...")
        mm_signals = self.detect_portfolio_signals(mm_data)
        
        # Simular trading
        print("Simulando trading Butterworth...")
        butter_history, butter_positions = self.simulate_portfolio_trading(butter_signals, initial_capital)
        
        print("Simulando trading Média Móvel...")
        mm_history, mm_positions = self.simulate_portfolio_trading(mm_signals, initial_capital)
        
        # Calcular métricas
        butter_metrics = self.calculate_portfolio_metrics(butter_history, initial_capital)
        mm_metrics = self.calculate_portfolio_metrics(mm_history, initial_capital)
        
        # Criar visualizações
        self.plot_portfolio_comparison(butter_history, mm_history, butter_metrics, mm_metrics)
        
        # Salvar resultados
        self.save_portfolio_results(butter_history, mm_history, butter_metrics, mm_metrics, 
                                   butter_positions, mm_positions)
        
        # Mostrar resultados
        self.print_portfolio_results(butter_metrics, mm_metrics)
        
        return {
            'butterworth': {'history': butter_history, 'metrics': butter_metrics, 'positions': butter_positions},
            'moving_average': {'history': mm_history, 'metrics': mm_metrics, 'positions': mm_positions}
        }

    def save_portfolio_results(self, butter_history, mm_history, butter_metrics, mm_metrics,
                              butter_positions, mm_positions):
        """
        Salva resultados da análise de portfólio
        """
        # Salvar histórico de trades
        if not butter_history.empty:
            butter_file = self.csv_dir / 'portfolio_butterworth_trades.csv'
            butter_history.to_csv(butter_file, index=False)

        if not mm_history.empty:
            mm_file = self.csv_dir / 'portfolio_mm_trades.csv'
            mm_history.to_csv(mm_file, index=False)

        # Salvar posições finais
        positions_data = []
        for stock in self.stocks:
            positions_data.append({
                'Stock': stock,
                'Butterworth_Position': butter_positions.get(stock, 0),
                'MM_Position': mm_positions.get(stock, 0)
            })

        positions_df = pd.DataFrame(positions_data)
        positions_file = self.csv_dir / 'portfolio_final_positions.csv'
        positions_df.to_csv(positions_file, index=False)

        # Salvar métricas comparativas
        comparison_data = {
            'Metric': ['Capital Inicial', 'Valor Final', 'Retorno Total', 'Retorno %',
                      'Volatilidade %', 'Sharpe Ratio', 'Max Drawdown %', 'Total Trades'],
            'Butterworth': [
                butter_metrics.get('initial_capital', 0),
                butter_metrics.get('final_value', 0),
                butter_metrics.get('total_return', 0),
                butter_metrics.get('return_pct', 0),
                butter_metrics.get('volatility', 0),
                butter_metrics.get('sharpe_ratio', 0),
                butter_metrics.get('max_drawdown', 0),
                butter_metrics.get('total_trades', 0)
            ],
            'Moving_Average': [
                mm_metrics.get('initial_capital', 0),
                mm_metrics.get('final_value', 0),
                mm_metrics.get('total_return', 0),
                mm_metrics.get('return_pct', 0),
                mm_metrics.get('volatility', 0),
                mm_metrics.get('sharpe_ratio', 0),
                mm_metrics.get('max_drawdown', 0),
                mm_metrics.get('total_trades', 0)
            ]
        }

        comparison_df = pd.DataFrame(comparison_data)
        comparison_file = self.csv_dir / 'portfolio_metrics_comparison.csv'
        comparison_df.to_csv(comparison_file, index=False)

        print(f"Resultados salvos em: {self.csv_dir}")

    def print_portfolio_results(self, butter_metrics, mm_metrics):
        """
        Imprime resultados da análise de portfólio
        """
        print("\n" + "=" * 70)
        print("RESULTADOS DA ANÁLISE DE PORTFÓLIO COMBINADO")
        print("=" * 70)

        print(f"\n📊 CAPITAL INICIAL: R$ {butter_metrics.get('initial_capital', 0):,.2f}")
        print(f"📈 PERÍODO: 1 ano (252 dias úteis)")
        print(f"🏢 AÇÕES: {len(self.stocks)} ações brasileiras")

        print(f"\n🔵 ESTRATÉGIA BUTTERWORTH:")
        print(f"   Valor Final: R$ {butter_metrics.get('final_value', 0):,.2f}")
        print(f"   Retorno: R$ {butter_metrics.get('total_return', 0):,.2f} ({butter_metrics.get('return_pct', 0):.2f}%)")
        print(f"   💰 INVESTIMENTO REAL:")
        print(f"      Total Investido: R$ {butter_metrics.get('total_invested', 0):,.2f}")
        print(f"      Total Recuperado: R$ {butter_metrics.get('total_recovered', 0):,.2f}")
        print(f"      Investimento Líquido: R$ {butter_metrics.get('net_invested', 0):,.2f}")
        print(f"      Retorno sobre Investido: {butter_metrics.get('invested_return_pct', 0):.2f}%")
        print(f"   Volatilidade: {butter_metrics.get('volatility', 0):.2f}%")
        print(f"   Sharpe Ratio: {butter_metrics.get('sharpe_ratio', 0):.3f}")
        print(f"   Max Drawdown: {butter_metrics.get('max_drawdown', 0):.2f}%")
        print(f"   Total Trades: {butter_metrics.get('total_trades', 0)}")

        print(f"\n🟠 ESTRATÉGIA MÉDIA MÓVEL:")
        print(f"   Valor Final: R$ {mm_metrics.get('final_value', 0):,.2f}")
        print(f"   Retorno: R$ {mm_metrics.get('total_return', 0):,.2f} ({mm_metrics.get('return_pct', 0):.2f}%)")
        print(f"   💰 INVESTIMENTO REAL:")
        print(f"      Total Investido: R$ {mm_metrics.get('total_invested', 0):,.2f}")
        print(f"      Total Recuperado: R$ {mm_metrics.get('total_recovered', 0):,.2f}")
        print(f"      Investimento Líquido: R$ {mm_metrics.get('net_invested', 0):,.2f}")
        print(f"      Retorno sobre Investido: {mm_metrics.get('invested_return_pct', 0):.2f}%")
        print(f"   Volatilidade: {mm_metrics.get('volatility', 0):.2f}%")
        print(f"   Sharpe Ratio: {mm_metrics.get('sharpe_ratio', 0):.3f}")
        print(f"   Max Drawdown: {mm_metrics.get('max_drawdown', 0):.2f}%")
        print(f"   Total Trades: {mm_metrics.get('total_trades', 0)}")

        # Determinar vencedor
        butter_return = butter_metrics.get('return_pct', 0)
        mm_return = mm_metrics.get('return_pct', 0)

        if mm_return > butter_return:
            winner = "MÉDIA MÓVEL"
            advantage = mm_return - butter_return
            print(f"\n🏆 VENCEDOR: {winner}")
            print(f"   Vantagem: +{advantage:.2f} pontos percentuais")
        elif butter_return > mm_return:
            winner = "BUTTERWORTH"
            advantage = butter_return - mm_return
            print(f"\n🏆 VENCEDOR: {winner}")
            print(f"   Vantagem: +{advantage:.2f} pontos percentuais")
        else:
            print(f"\n🤝 EMPATE: Ambas as estratégias tiveram performance similar")

        # Análise de eficiência
        butter_trades = butter_metrics.get('total_trades', 1)
        mm_trades = mm_metrics.get('total_trades', 1)

        butter_return_per_trade = butter_return / butter_trades if butter_trades > 0 else 0
        mm_return_per_trade = mm_return / mm_trades if mm_trades > 0 else 0

        print(f"\n📊 EFICIÊNCIA POR TRADE:")
        print(f"   Butterworth: {butter_return_per_trade:.4f}% por trade")
        print(f"   Média Móvel: {mm_return_per_trade:.4f}% por trade")

        if mm_return_per_trade > butter_return_per_trade:
            print(f"   ✅ Média Móvel é {mm_return_per_trade/butter_return_per_trade:.2f}x mais eficiente")
        elif butter_return_per_trade > mm_return_per_trade:
            print(f"   ✅ Butterworth é {butter_return_per_trade/mm_return_per_trade:.2f}x mais eficiente")

if __name__ == "__main__":
    analyzer = PortfolioTradingAnalysis()
    results = analyzer.run_portfolio_analysis(initial_capital=100000)
