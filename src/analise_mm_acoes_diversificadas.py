#!/usr/bin/env python3
"""
Script para análise das 20 ações diversificadas usando médias móveis
Análise técnica com médias móveis de 25, 50 e 200 dias
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
import sys
from datetime import datetime, timedelta

# Adicionar o diretório src ao path para importar functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling

warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

def carregar_acoes_diversificadas():
    """
    Carrega as 20 ações do arquivo CSV de diversificação
    """
    try:
        csv_path = 'results/csv/correlation_data/acoes_diversificacao.csv'
        df = pd.read_csv(csv_path)

        # Pegar apenas as primeiras 20 ações (excluindo linha vazia no final)
        acoes = []
        for _, row in df.head(20).iterrows():
            ticker = row['Ticker'] + '.SA'
            nome = row['Nome']
            acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo CSV")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def corrigir_valores_zero_ultimo_dia(dados):
    """
    Corrige valores zero no último dia substituindo pelo valor do dia anterior

    Args:
        dados: DataFrame com dados históricos

    Returns:
        DataFrame com valores corrigidos
    """
    if len(dados) < 2:
        return dados

    # Colunas OHLC para verificar
    colunas_ohlc = ['Open', 'High', 'Low', 'Close']

    # Verificar e corrigir valores zero no último dia
    for coluna in colunas_ohlc:
        if coluna in dados.columns:
            ultimo_valor = dados[coluna].iloc[-1]
            if ultimo_valor == 0 or pd.isna(ultimo_valor):
                # Substituir pelo valor do penúltimo dia
                penultimo_valor = dados[coluna].iloc[-2]
                if not pd.isna(penultimo_valor) and penultimo_valor != 0:
                    dados.loc[dados.index[-1], coluna] = penultimo_valor
                    print(f"     ⚠️  Corrigido {coluna} do último dia: 0 → {penultimo_valor:.2f}")

    return dados

def calcular_medias_moveis(dados):
    """
    Calcula médias móveis de 10, 25, 50, 100 e 200 dias baseadas na média OHLC

    Args:
        dados: DataFrame com dados históricos

    Returns:
        DataFrame com médias móveis adicionadas
    """
    # Corrigir valores zero no último dia
    dados = corrigir_valores_zero_ultimo_dia(dados)

    # Calcular média OHLC
    dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4

    # Calcular médias móveis tradicionais (forward-looking)
    dados['MM10'] = dados['Media_OHLC'].ewm(span=10).mean()
    dados['MM25'] = dados['Media_OHLC'].ewm(span=25).mean()
    dados['MM50'] = dados['Media_OHLC'].ewm(span=50).mean()
    dados['MM100'] = dados['Media_OHLC'].ewm(span=100).mean()
    dados['MM200'] = dados['Media_OHLC'].ewm(span=200).mean()





    return dados

def obter_dados_com_mm(ticker, nome):
    """
    Obtém dados e calcula médias móveis

    Args:
        ticker: Símbolo da ação
        nome: Nome da empresa
    """
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome}")

        stock = yf.Ticker(ticker)
        # Obter 18 meses de dados para análise
        dados = stock.history(period="18mo")

        if dados.empty or len(dados) < 200:  # Mínimo para calcular MM200
            print(f"     ⚠️ Dados insuficientes ({len(dados) if not dados.empty else 0} dias)")
            return None

        print(f"     📅 Dados obtidos: {len(dados)} dias")
        # print(dados.tail())
        # Calcular médias móveis
        dados = calcular_medias_moveis(dados)

        # Calcular spread usando edge_rolling
        try:
            spread_estimado = edge_rolling(dados[['Open', 'High', 'Low', 'Close']], window=20)
            dados['Spread'] = spread_estimado * 100  # Converter para percentual
        except Exception as e:
            print(f"     ⚠️ Erro ao calcular spread: {e}")
            # Fallback: usar volatilidade como proxy para spread
            returns = dados['Media_OHLC'].pct_change().dropna()
            volatilidade = returns.rolling(window=20).std()
            dados['Spread'] = volatilidade * 100

        # Preencher NaN no spread
        dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())

        # Calcular tendência como diferença entre médias OHLC consecutivas
        dados['Tendencia'] = dados['Media_OHLC'].diff()

        # Pegar últimos 12 meses dos dados
        dados_12m = dados.tail(252)

        print(f"     ✅ {len(dados_12m)} dias (com MM10, MM25, MM50, MM100 e MM200)")

        return dados_12m

    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:50]}")
        return None

def analisar_tendencia_mm(dados):
    """
    Analisa a tendência baseada nas médias móveis tradicionais
    """
    if dados is None or len(dados) < 50:
        return "Indefinida", "gray"

    preco_atual = dados['Media_OHLC'].iloc[-1]
    mm10_atual = dados['MM10'].iloc[-1]
    mm25_atual = dados['MM25'].iloc[-1]
    mm50_atual = dados['MM50'].iloc[-1]
    mm100_atual = dados['MM100'].iloc[-1]
    mm200_atual = dados['MM200'].iloc[-1]

    # Verificar se os valores estão válidos
    if pd.isna(mm10_atual) or pd.isna(mm25_atual) or pd.isna(mm50_atual) or pd.isna(mm100_atual) or pd.isna(mm200_atual):
        return "Indefinida", "gray"

    # Análise de tendência baseada em médias móveis tradicionais
    if preco_atual > mm10_atual > mm25_atual > mm50_atual > mm100_atual > mm200_atual:
        return "Altista Forte", "darkgreen"
    elif preco_atual > mm10_atual > mm25_atual > mm100_atual > mm200_atual:
        return "Altista Moderada", "green"
    elif preco_atual < mm10_atual < mm25_atual < mm50_atual < mm100_atual < mm200_atual:
        return "Baixista Forte", "darkred"
    elif preco_atual < mm10_atual < mm25_atual < mm100_atual < mm200_atual:
        return "Baixista Moderada", "red"
    else:
        return "Lateral", "orange"

def criar_grafico_mm(ticker, nome, dados):
    """
    Cria gráfico com média OHLC e médias móveis tradicionais
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

    # Calcular estatísticas
    preco_inicial = dados['Media_OHLC'].iloc[0]
    preco_final = dados['Media_OHLC'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100

    # Análise de tendência
    tendencia, cor_tendencia = analisar_tendencia_mm(dados)

    # Gráfico 1: Média OHLC + Médias Móveis Tradicionais
    ax1.plot(dados.index, dados['Media_OHLC'], linewidth=2.5, color="#eb1414",
             label='Média OHLC', zorder=3)

    # Médias móveis tradicionais
    ax1.plot(dados.index, dados['MM10'], linewidth=5, color='blue',
             label='MM 10 dias', alpha=0.8, zorder=2)
    ax1.plot(dados.index, dados['MM25'], linewidth=2, color='green',
             label='MM 25 dias', alpha=0.8, zorder=2)
    ax1.plot(dados.index, dados['MM50'], linewidth=2, color='orange',
             label='MM 50 dias', alpha=0.8, zorder=2)
    ax1.plot(dados.index, dados['MM100'], linewidth=2, color='purple',
             label='MM 100 dias', alpha=0.8, zorder=2)
    ax1.plot(dados.index, dados['MM200'], linewidth=2, color='red',
             label='MM 200 dias', alpha=0.8, zorder=2)


    # Área entre preço e MM200 para visualizar posicionamento
    ax1.fill_between(dados.index, dados['Media_OHLC'], dados['MM200'],
                     where=(dados['Media_OHLC'] >= dados['MM200']),
                     color='lightgreen', alpha=0.3, interpolate=True,
                     label='Preço > MM200')
    ax1.fill_between(dados.index, dados['Media_OHLC'], dados['MM200'],
                     where=(dados['Media_OHLC'] < dados['MM200']),
                     color='lightcoral', alpha=0.3, interpolate=True,
                     label='Preço < MM200')

    # Título do gráfico
    titulo_base = f'{nome} ({ticker.replace(".SA", "")}) - Médias Móveis Exponencial'

    ax1.set_title(titulo_base, fontsize=16, fontweight='bold')
    ax1.set_ylabel('Valor (R$)', fontsize=12)

    # Posicionar legenda no canto superior esquerdo
    ax1.legend(loc='upper left', fontsize=9, ncol=2, framealpha=0.9,
              fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3)

    # Estatísticas no gráfico - posicionar no canto inferior esquerdo
    mm10_atual = dados['MM10'].iloc[-1] if not pd.isna(dados['MM10'].iloc[-1]) else 0
    mm25_atual = dados['MM25'].iloc[-1] if not pd.isna(dados['MM25'].iloc[-1]) else 0
    mm50_atual = dados['MM50'].iloc[-1] if not pd.isna(dados['MM50'].iloc[-1]) else 0
    mm100_atual = dados['MM100'].iloc[-1] if not pd.isna(dados['MM100'].iloc[-1]) else 0
    mm200_atual = dados['MM200'].iloc[-1] if not pd.isna(dados['MM200'].iloc[-1]) else 0

    stats_text = f'Média OHLC: R$ {preco_final:.2f} | Performance: {performance:+.1f}%\n'
    stats_text += f'MM10: R$ {mm10_atual:.2f} | MM25: R$ {mm25_atual:.2f} | MM50: R$ {mm50_atual:.2f}\n'
    stats_text += f'MM100: R$ {mm100_atual:.2f} | MM200: R$ {mm200_atual:.2f}\n'
    stats_text += f'Tendência: {tendencia}'

    # Posicionar no canto inferior esquerdo para não sobrepor dados
    ax1.text(0.02, 0.02, stats_text, transform=ax1.transAxes,
             fontsize=10, verticalalignment='bottom',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.95,
                      edgecolor=cor_tendencia, linewidth=2))

    # Gráfico 2: Spread e Tendência
    if 'Spread' in dados.columns and 'Tendencia' in dados.columns:
        ax2_twin = ax2.twinx()

        # Spread no eixo esquerdo
        line1 = ax2.plot(dados.index, dados['Spread'], alpha=0.7, color='purple',
                        linewidth=1.5, label='Spread (%)')
        ax2.set_ylabel('Spread (%)', fontsize=12, color='purple')
        ax2.tick_params(axis='y', labelcolor='purple')

        # Tendência no eixo direito
        line2 = ax2_twin.plot(dados.index, dados['Tendencia'], alpha=0.7, color='brown',
                             linewidth=1.5, label='Tendência (R$)')
        ax2_twin.set_ylabel('Tendência (R$)', fontsize=12, color='brown')
        ax2_twin.tick_params(axis='y', labelcolor='brown')

        # Linha zero para tendência
        ax2_twin.axhline(y=0, color='gray', linestyle='--', alpha=0.5)

        ax2.set_title('Spread Bid/Ask (%) e Tendência Diária', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)

        # Combinar legendas - posicionar no canto superior esquerdo
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='upper left', framealpha=0.9,
                  fancybox=True, shadow=True)

    ax2.set_xlabel('Data', fontsize=12)

    plt.tight_layout()

    # Ensure directory exists
    os.makedirs('results/figures/mm_analysis', exist_ok=True)
    os.makedirs('results/csv/mm_analysis/individual_stocks', exist_ok=True)

    # Salvar gráfico
    nome_arquivo = f"results/figures/mm_analysis/mm_{ticker.replace('.SA', '')}_12m.png"
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

    # Salvar dados de séries temporais em CSV
    ticker_clean = ticker.replace('.SA', '')
    csv_dados = dados[['Media_OHLC', 'MM10', 'MM25', 'MM50', 'MM100', 'MM200', 'Spread', 'Tendencia']].copy()
    csv_dados.index.name = 'Data'

    # Renomear colunas para melhor clareza
    csv_dados.columns = [
        'Preco_Media_OHLC',
        'MM10_dias',
        'MM25_dias',
        'MM50_dias',
        'MM100_dias',
        'MM200_dias',
        'Spread_Percent',
        'Tendencia_Diaria'
    ]

    csv_filename = f"results/csv/mm_analysis/individual_stocks/mm_series_{ticker_clean}.csv"
    csv_dados.to_csv(csv_filename)

    print(f"     💾 {nome_arquivo}")
    print(f"     📊 {csv_filename}")

    resultado = {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_atual': preco_final,  # Média OHLC
        'mm10': mm10_atual,
        'mm25': mm25_atual,
        'mm50': mm50_atual,
        'mm100': mm100_atual,
        'mm200': mm200_atual,
        'tendencia': tendencia,
        'cor_tendencia': cor_tendencia
    }

    return resultado

def gerar_relatorio_mm(resultados):
    """
    Gera relatório detalhado da análise com médias móveis
    """
    print("\n" + "="*140)
    print("📊 RELATÓRIO ANÁLISE TÉCNICA - MÉDIAS MÓVEIS (10, 25, 50, 100, 200 DIAS)")
    print("="*140)

    # Ordenar por performance
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)

    print(f"{'#':<3} {'Ticker':<8} {'Nome':<20} {'Perf%':<8} {'Média OHLC':<12} {'MM10':<8} {'MM25':<8} {'MM50':<8} {'MM100':<8} {'MM200':<8} {'Tendência':<15}")
    print("-" * 140)

    for i, r in enumerate(resultados_ord, 1):
        emoji = "🟢" if r['performance'] >= 0 else "🔴"

        print(f"{i:<3} {emoji} {r['ticker'].replace('.SA', ''):<6} "
              f"{r['nome'][:19]:<20} {r['performance']:>+6.1f}% "
              f"{r['preco_atual']:>10.2f} {r['mm10']:>6.2f} {r['mm25']:>6.2f} {r['mm50']:>6.2f} {r['mm100']:>6.2f} {r['mm200']:>6.2f} "
              f"{r['tendencia']:<15}")

    # Estatísticas por tendência
    print("\n" + "="*120)
    print("📊 ESTATÍSTICAS POR TENDÊNCIA")
    print("="*120)

    tendencias = {}
    for r in resultados:
        tend = r['tendencia']
        if tend not in tendencias:
            tendencias[tend] = []
        tendencias[tend].append(r['performance'])

    for tendencia, performances in tendencias.items():
        count = len(performances)
        media = np.mean(performances)
        print(f"{tendencia:<20}: {count:2d} ações | Performance média: {media:+6.1f}%")

    # Análise de posicionamento
    print("\n" + "="*140)
    print("🎯 ANÁLISE DE POSICIONAMENTO")
    print("="*140)

    acima_mm10 = len([r for r in resultados if r['preco_atual'] > r['mm10']])
    acima_mm25 = len([r for r in resultados if r['preco_atual'] > r['mm25']])
    acima_mm50 = len([r for r in resultados if r['preco_atual'] > r['mm50']])
    acima_mm100 = len([r for r in resultados if r['preco_atual'] > r['mm100']])
    acima_mm200 = len([r for r in resultados if r['preco_atual'] > r['mm200']])
    mm10_acima_mm25 = len([r for r in resultados if r['mm10'] > r['mm25']])
    mm25_acima_mm50 = len([r for r in resultados if r['mm25'] > r['mm50']])
    mm50_acima_mm100 = len([r for r in resultados if r['mm50'] > r['mm100']])
    mm100_acima_mm200 = len([r for r in resultados if r['mm100'] > r['mm200']])

    print(f"Ações com Média OHLC acima da MM10:   {acima_mm10:2d}/{len(resultados)} ({acima_mm10/len(resultados)*100:.1f}%)")
    print(f"Ações com Média OHLC acima da MM25:   {acima_mm25:2d}/{len(resultados)} ({acima_mm25/len(resultados)*100:.1f}%)")
    print(f"Ações com Média OHLC acima da MM50:   {acima_mm50:2d}/{len(resultados)} ({acima_mm50/len(resultados)*100:.1f}%)")
    print(f"Ações com Média OHLC acima da MM100:  {acima_mm100:2d}/{len(resultados)} ({acima_mm100/len(resultados)*100:.1f}%)")
    print(f"Ações com Média OHLC acima da MM200:  {acima_mm200:2d}/{len(resultados)} ({acima_mm200/len(resultados)*100:.1f}%)")
    print(f"Ações com MM10 > MM25:               {mm10_acima_mm25:2d}/{len(resultados)} ({mm10_acima_mm25/len(resultados)*100:.1f}%)")
    print(f"Ações com MM25 > MM50:               {mm25_acima_mm50:2d}/{len(resultados)} ({mm25_acima_mm50/len(resultados)*100:.1f}%)")
    print(f"Ações com MM50 > MM100:              {mm50_acima_mm100:2d}/{len(resultados)} ({mm50_acima_mm100/len(resultados)*100:.1f}%)")
    print(f"Ações com MM100 > MM200:             {mm100_acima_mm200:2d}/{len(resultados)} ({mm100_acima_mm200/len(resultados)*100:.1f}%)")

    # Análise de alinhamento das médias móveis
    print(f"\n📈 ANÁLISE DE ALINHAMENTO DAS MÉDIAS MÓVEIS")
    print("="*140)

    alinhamento_altista = len([r for r in resultados if r['mm10'] > r['mm25'] > r['mm50'] > r['mm100'] > r['mm200']])
    alinhamento_baixista = len([r for r in resultados if r['mm10'] < r['mm25'] < r['mm50'] < r['mm100'] < r['mm200']])

    print(f"Alinhamento Altista (MM10 > MM25 > MM50 > MM100 > MM200): {alinhamento_altista:2d}/{len(resultados)} ({alinhamento_altista/len(resultados)*100:.1f}%)")
    print(f"Alinhamento Baixista (MM10 < MM25 < MM50 < MM100 < MM200): {alinhamento_baixista:2d}/{len(resultados)} ({alinhamento_baixista/len(resultados)*100:.1f}%)")
    print(f"Sem alinhamento claro:                                    {len(resultados) - alinhamento_altista - alinhamento_baixista:2d}/{len(resultados)} ({(len(resultados) - alinhamento_altista - alinhamento_baixista)/len(resultados)*100:.1f}%)")

def main():
    print("📊 ANÁLISE COM MÉDIAS MÓVEIS - AÇÕES DIVERSIFICADAS")
    print("="*80)
    print("📊 Análise Técnica com:")
    print("   • Média OHLC (Open + Close + Low + High) / 4")
    print("   • Média Móvel de 10 dias (MM10) baseada na Média OHLC")
    print("   • Média Móvel de 25 dias (MM25) baseada na Média OHLC")
    print("   • Média Móvel de 50 dias (MM50) baseada na Média OHLC")
    print("   • Média Móvel de 100 dias (MM100) baseada na Média OHLC")
    print("   • Média Móvel de 200 dias (MM200) baseada na Média OHLC")
    print("   • Análise de tendência baseada no alinhamento das médias")
    print("   • Spread Bid/Ask e tendência diária")

    # Carregar ações do arquivo CSV
    acoes = carregar_acoes_diversificadas()

    if not acoes:
        print("❌ Não foi possível carregar as ações do arquivo CSV")
        return

    print(f"\n📋 Serão analisadas {len(acoes)} ações diversificadas")
   
    print(f"\n📊 Iniciando análise com médias móveis...")

    resultados = []
    sucesso = 0
    erro = 0

    for i, (ticker, nome) in enumerate(acoes, 1):
        print(f"\n[{i:2d}/{len(acoes)}]", end=" ")

        dados = obter_dados_com_mm(ticker, nome)

        if dados is not None:
            resultado = criar_grafico_mm(ticker, nome, dados)
            resultados.append(resultado)
            sucesso += 1
        else:
            erro += 1

    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")

    if resultados:
        # Gerar relatório
        gerar_relatorio_mm(resultados)

        # Salvar resultados em CSV
        try:
            df_resultados = pd.DataFrame(resultados)
            os.makedirs('results/csv/mm_analysis', exist_ok=True)
            csv_path = 'results/csv/mm_analysis/resultados_mm.csv'
            df_resultados.to_csv(csv_path, index=False)
            print(f"   • Resultados resumo salvos em: {csv_path}")
            print(f"   • Séries temporais individuais salvas em: results/csv/mm_analysis/individual_stocks/")
        except Exception as e:
            print(f"   ⚠️ Erro ao salvar CSV: {e}")

    else:
        print("\n❌ Nenhum gráfico foi gerado!")

if __name__ == "__main__":
    main()
