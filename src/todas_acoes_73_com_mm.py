#!/usr/bin/env python3
"""
Script para gerar gráficos de TODAS as 73 ações brasileiras COM MÉDIAS MÓVEIS (50 e 200 dias)
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

# TODAS as 73 ações brasileiras solicitadas
TODAS_ACOES = [
    ('ABEV3.SA', 'AMBEV S/A'), ('AZUL4.SA', 'AZUL'), ('B3SA3.SA', 'B3'), ('BBAS3.SA', 'BRASIL'),
    ('BBDC3.SA', 'BRADESCO'), ('BBDC4.SA', 'BRADESCO'), ('BBSE3.SA', 'BBSEGURIDADE'), ('BEEF3.SA', 'MINERVA'),
    ('BPAC11.SA', 'BTGP BANCO'), ('BRAP4.SA', 'BRADESPAR'), ('BRDT3.SA', 'PETROBRAS BR'), ('BRFS3.SA', 'BRF SA'),
    ('BRKM5.SA', 'BRASKEM'), ('BRML3.SA', 'BR MALLS PAR'), ('BTOW3.SA', 'B2W DIGITAL'), ('CCRO3.SA', 'CCR SA'),
    ('CIEL3.SA', 'CIELO'), ('CMIG4.SA', 'CEMIG'), ('COGN3.SA', 'COGNA ON'), ('CPFE3.SA', 'CPFL ENERGIA'),
    ('CRFB3.SA', 'CARREFOUR BR'), ('CSAN3.SA', 'COSAN'), ('CSNA3.SA', 'SID NACIONAL'), ('CVCB3.SA', 'CVC BRASIL'),
    ('CYRE3.SA', 'CYRELA REALT'), ('ECOR3.SA', 'ECORODOVIAS'), ('EGIE3.SA', 'ENGIE BRASIL'), ('ELET3.SA', 'ELETROBRAS'),
    ('ELET6.SA', 'ELETROBRAS'), ('EMBR3.SA', 'EMBRAER'), ('ENBR3.SA', 'ENERGIAS BR'), ('ENGI11.SA', 'ENERGISA'),
    ('EQTL3.SA', 'EQUATORIAL'), ('EZTC3.SA', 'EZTEC'), ('FLRY3.SA', 'FLEURY'), ('GGBR4.SA', 'GERDAU'),
    ('GNDI3.SA', 'INTERMEDICA'), ('GOAU4.SA', 'GERDAU MET'), ('GOLL4.SA', 'GOL'), ('HAPV3.SA', 'HAPVIDA'),
    ('HGTX3.SA', 'CIA HERING'), ('HYPE3.SA', 'HYPERA'), ('IGTA3.SA', 'IGUATEMI'), ('IRBR3.SA', 'IRBBRASIL RE'),
    ('ITSA4.SA', 'ITAUSA'), ('ITUB4.SA', 'ITAUUNIBANCO'), ('JBSS3.SA', 'JBS'), ('KLBN11.SA', 'KLABIN S/A'),
    ('LAME4.SA', 'LOJAS AMERIC'), ('LREN3.SA', 'LOJAS RENNER'), ('MGLU3.SA', 'MAGAZ LUIZA'), ('MRFG3.SA', 'MARFRIG'),
    ('MRVE3.SA', 'MRV'), ('MULT3.SA', 'MULTIPLAN'), ('NTCO3.SA', 'GRUPO NATURA'), ('PCAR3.SA', 'P.ACUCAR-CBD'),
    ('PETR3.SA', 'PETROBRAS'), ('PETR4.SA', 'PETROBRAS'), ('PRIO3.SA', 'PETRORIO'), ('QUAL3.SA', 'QUALICORP'),
    ('RADL3.SA', 'RAIADROGASIL'), ('RAIL3.SA', 'RUMO S.A.'), ('RENT3.SA', 'LOCALIZA'), ('SANB11.SA', 'SANTANDER BR'),
    ('SBSP3.SA', 'SABESP'), ('SULA11.SA', 'SUL AMERICA'), ('SUZB3.SA', 'SUZANO S.A.'), ('TAEE11.SA', 'TAESA'),
    ('TIMS3.SA', 'TIM'), ('TOTS3.SA', 'TOTVS'), ('UGPA3.SA', 'ULTRAPAR'), ('USIM5.SA', 'USIMINAS'),
    ('VALE3.SA', 'VALE'), ('VIVT4.SA', 'TELEF BRASIL'), ('VVAR3.SA', 'VIAVAREJO'), ('WEGE3.SA', 'WEG'),
    ('YDUQ3.SA', 'YDUQS PART')
]

def obter_dados_com_mm(ticker, nome):
    """Obtém dados de 18 meses e calcula médias móveis"""
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome[:20]}")
        
        stock = yf.Ticker(ticker)
        # Obter 15 meses para ter dados suficientes para MM200
        dados = stock.history(period="18mo")
        
        if dados.empty or len(dados) < 200:
            print(f"     ⚠️ Dados insuficientes ({len(dados) if not dados.empty else 0} dias)")
            return None
        
        # Calcular médias móveis
        dados['MM200'] = dados['Close'].rolling(window=200).mean()
        
        # Pegar apenas os últimos 12 meses para exibição
        dados_12m = dados.tail(252)  # ~252 dias úteis em 12 meses
            
        print(f"     ✅ {len(dados_12m)} dias (com MMs)")
        return dados_12m
        
    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:30]}")
        return None

def analisar_tendencia_mm(dados):
    """Analisa tendência baseada nas médias móveis"""
    if dados is None or len(dados) < 50:
        return "Indefinida", "gray"

    preco_atual = dados['Close'].iloc[-1]
    mm200_atual = dados['MM200'].iloc[-1]

    if pd.isna(mm200_atual):
        return "Indefinida", "gray"

    if preco_atual > mm200_atual:
        return "Altista", "darkgreen"
    elif preco_atual < mm200_atual:
        return "Baixista", "darkred"
    else:
        return "Lateral", "orange"

def criar_grafico_mm(ticker, nome, dados):
    """Cria gráfico com preço, médias móveis e volume"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # Calcular estatísticas
    preco_inicial = dados['Close'].iloc[0]
    preco_final = dados['Close'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100
    
    # Análise de tendência
    tendencia, cor_tendencia = analisar_tendencia_mm(dados)
    
    # Gráfico 1: Preço + Médias Móveis
    ax1.plot(dados.index, dados['Close'], linewidth=2.5, color='blue', label='Preço', zorder=3)
    ax1.plot(dados.index, dados['MM200'], linewidth=2, color='red', label='MM200', alpha=0.8, zorder=2)

    # Área colorida para posição relativa
    ax1.fill_between(dados.index, dados['Close'], dados['MM200'],
                     where=(dados['Close'] >= dados['MM200']),
                     color='lightgreen', alpha=0.2, interpolate=True)
    ax1.fill_between(dados.index, dados['Close'], dados['MM200'],
                     where=(dados['Close'] < dados['MM200']),
                     color='lightcoral', alpha=0.2, interpolate=True)

    ax1.set_title(f'{nome} ({ticker.replace(".SA", "")}) - Preço + MM200',
                  fontsize=14, fontweight='bold')
    ax1.set_ylabel('Preço (R$)')
    ax1.legend(loc='upper left', fontsize=9)
    ax1.grid(True, alpha=0.3)
    
    # Estatísticas
    mm200_atual = dados['MM200'].iloc[-1] if not pd.isna(dados['MM200'].iloc[-1]) else 0

    stats_text = f'Preço: R$ {preco_final:.2f} | Perf: {performance:+.1f}%\n'
    stats_text += f'MM200: R$ {mm200_atual:.2f}\n'
    stats_text += f'{tendencia}'
    
    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, 
             fontsize=9, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.9, 
                      edgecolor=cor_tendencia, linewidth=1.5))
    
    # Gráfico 2: Volume
    ax2.bar(dados.index, dados['Volume']/1e6, alpha=0.6, color='purple', width=0.8)
    ax2.set_title('Volume (Milhões)', fontsize=12)
    ax2.set_xlabel('Data')
    ax2.set_ylabel('Volume (Mi)')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('../results/figures/moving_averages', exist_ok=True)

    # Salvar
    nome_arquivo = f"../results/figures/moving_averages/mm73_{ticker.replace('.SA', '')}.png"
    plt.savefig(nome_arquivo, dpi=200, bbox_inches='tight')
    plt.close()

    print(f"     💾 {nome_arquivo}")
    
    return {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_atual': preco_final,
        'mm200': mm200_atual,
        'tendencia': tendencia
    }

def criar_resumo_73_mm(resultados):
    """Cria resumo das 73 ações com médias móveis"""
    print("\n📊 Criando resumo das 73 ações com médias móveis...")
    
    # Gráfico de performance por tendência
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    
    # Separar por tendência
    tendencias = {}
    for r in resultados:
        tend = r['tendencia']
        if tend not in tendencias:
            tendencias[tend] = []
        tendencias[tend].append(r)
    
    # Gráfico 1: Pizza das tendências
    labels = list(tendencias.keys())
    sizes = [len(tendencias[label]) for label in labels]
    colors = ['darkgreen', 'green', 'orange', 'red', 'darkred', 'gray'][:len(labels)]
    
    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('Distribuição de Tendências (73 Ações)', fontsize=14, fontweight='bold')
    
    # Gráfico 2: Performance por tendência
    for i, (tend, acoes) in enumerate(tendencias.items()):
        performances = [a['performance'] for a in acoes]
        ax2.scatter([i] * len(performances), performances, 
                   color=colors[i % len(colors)], alpha=0.7, s=50, label=tend)
    
    ax2.set_xlabel('Tendência')
    ax2.set_ylabel('Performance (%)')
    ax2.set_title('Performance por Tendência', fontsize=14, fontweight='bold')
    ax2.set_xticks(range(len(labels)))
    ax2.set_xticklabels(labels, rotation=45)
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    # Gráfico 3: Top 20 performances
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)
    top_20 = resultados_ord[:20]
    
    tickers_top = [r['ticker'].replace('.SA', '') for r in top_20]
    perf_top = [r['performance'] for r in top_20]
    
    bars = ax3.bar(range(len(tickers_top)), perf_top, 
                   color=['green' if p >= 0 else 'red' for p in perf_top], alpha=0.7)
    ax3.set_title('TOP 20 - Performance', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Ações')
    ax3.set_ylabel('Performance (%)')
    ax3.set_xticks(range(len(tickers_top)))
    ax3.set_xticklabels(tickers_top, rotation=90, fontsize=8)
    ax3.grid(True, alpha=0.3, axis='y')
    
    # Gráfico 4: Posicionamento MM
    acima_mm200 = len([r for r in resultados if r['preco_atual'] > r['mm200']])
    abaixo_mm200 = len(resultados) - acima_mm200

    categorias = ['Acima MM200', 'Abaixo MM200']
    valores = [acima_mm200, abaixo_mm200]
    cores_pos = ['darkgreen', 'darkred']
    
    ax4.bar(categorias, valores, color=cores_pos, alpha=0.7)
    ax4.set_title('Posicionamento vs Médias Móveis', fontsize=14, fontweight='bold')
    ax4.set_ylabel('Número de Ações')
    
    # Adicionar valores nas barras
    for i, v in enumerate(valores):
        ax4.text(i, v + 1, str(v), ha='center', fontweight='bold')
    
    plt.suptitle('Dashboard - 73 Ações Brasileiras com Médias Móveis', fontsize=18, fontweight='bold')
    plt.tight_layout()

    # Ensure directory exists
    import os
    os.makedirs('../results/figures/dashboards', exist_ok=True)

    plt.savefig('../results/figures/dashboards/dashboard_73_acoes_mm.png', dpi=200, bbox_inches='tight')
    plt.close()

    print("   💾 ../results/figures/dashboards/dashboard_73_acoes_mm.png")

def main():
    print("🚀 TODAS AS 73 AÇÕES COM MÉDIAS MÓVEIS (50 e 200 dias)")
    print("="*70)
    print(f"📋 Total: {len(TODAS_ACOES)} ações brasileiras")
    print("📊 Cada gráfico terá:")
    print("   • Preço de fechamento (linha azul)")
    print("   • Média Móvel 200 dias (linha vermelha)")
    print("   • Área colorida (verde=preço>MM200, vermelho=preço<MM200)")
    print("   • Análise automática de tendência")
    print("   • Volume de negociação")
    
    print(f"\n⚠️  ATENÇÃO: Serão gerados {len(TODAS_ACOES)} gráficos PNG!")
    print("⏱️  Tempo estimado: 10-15 minutos")
    
    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return
    
    print(f"\n📈 Iniciando análise de {len(TODAS_ACOES)} ações com médias móveis...")
    
    resultados = []
    sucesso = 0
    erro = 0
    
    for i, (ticker, nome) in enumerate(TODAS_ACOES, 1):
        print(f"\n[{i:2d}/{len(TODAS_ACOES)}]", end=" ")
        
        dados = obter_dados_com_mm(ticker, nome)
        
        if dados is not None:
            resultado = criar_grafico_mm(ticker, nome, dados)
            resultados.append(resultado)
            sucesso += 1
        else:
            erro += 1
    
    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")
    
    if resultados:
        # Criar dashboard
        criar_resumo_73_mm(resultados)
        
        # Estatísticas finais
        print(f"\n📊 ESTATÍSTICAS FINAIS:")
        
        # Por tendência
        tendencias = {}
        for r in resultados:
            tend = r['tendencia']
            if tend not in tendencias:
                tendencias[tend] = []
            tendencias[tend].append(r['performance'])
        
        for tend, perfs in tendencias.items():
            print(f"   {tend:15s}: {len(perfs):2d} ações | Média: {np.mean(perfs):+6.1f}%")
        
        # Posicionamento
        acima_mm200 = len([r for r in resultados if r['preco_atual'] > r['mm200']])

        print(f"\n📈 POSICIONAMENTO:")
        print(f"   Preço > MM200: {acima_mm200:2d}/{len(resultados)} ({acima_mm200/len(resultados)*100:.1f}%)")
        
        print(f"\n📁 ARQUIVOS GERADOS: {sucesso + 1} arquivos PNG")
        print("   • 1 dashboard resumo")
        print(f"   • {sucesso} gráficos individuais com médias móveis")
        
    else:
        print("\n❌ Nenhum gráfico foi gerado!")

if __name__ == "__main__":
    main()
